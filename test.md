# Capstone Project Evaluation Report

**Student:** Jena
**Date:** [Evaluation Date]
**Total Score:** 65/70 points

---

## Section 1: Frontend (30 points)

### Task 1: Add 2 CSS Layout Feature Boxes (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** All three feature boxes (Adaptive Courses, Progress Tracking, Real-time Assessments) are present, using Flexbox for layout. Titles and structure are correct.
- **Evidence:**
  ```html
  <section class="feature-box">
    <div class="card-flex">
      <h4>Adaptive Courses</h4>
      ...
    </div>
    <div class="card-flex">
      <h4>Progress Tracking</h4>
      ...
    </div>
    <div class="card-flex">
      <h4>Real-time Assessments</h4>
      ...
    </div>
  </section>
  ```

### Task 2: Add 2 Bootstrap Cards (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Two Bootstrap cards (HTML Module, CSS Module) are implemented side by side using Bootstrap grid and card classes.
- **Evidence:**
  ```html
  <div class="row">
    <div class="col-md-6">
      <div class="card">
        <div class="card-body">
          <h3 class="card-title">HTML Module</h3>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-body">
          <h3 class="card-title">CSS Module</h3>
        </div>
      </div>
    </div>
  </div>
  ```

### Task 3: Email Validation (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Email validation checks for '@', updates DOM, and prevents form submission if invalid. Correct messages are shown.
- **Evidence:**
  ```js
  function validateEmail(event) {
    let emailInput = document.getElementById("email").value;
    if (emailInput.includes("@")) {
      document.getElementById("emailMessage").textContent = "Email accepted!";
      return true;
    } else {
      document.getElementById("emailMessage").textContent =
        "Invalid email address";
      return false;
    }
  }
  ```

### Task 4: Input Event Handling (5 points)

- **Score:** 4/5
- **Level:** Developing
- **Feedback:** The goal output updates as the user types, but the event listener is attached globally rather than directly to the input, which may cause unintended side effects.
- **Evidence:**
  ```js
  addEventListener("input", onkeypress);
  onkeypress = (event) => {
    let goalInput = document.getElementById("goalInput");
    document.getElementById("goalOutput").textContent =
      "Your goal: " + goalInput.value;
  };
  ```

### Task 5: Password Strength Checker (5 points)

- **Score:** 3/5
- **Level:** Developing
- **Feedback:** Checks password length and for numbers, but only considers passwords longer than 6 characters as strong (should be >=6). Also, the message is only set if password is >6 and contains a number, missing the case for exactly 6 characters.
- **Evidence:**
  ```js
  if (password?.length < 6) {
    setMessage("Weak password");
    setIsStrongPassword(false);
  } else if (password?.length > 6 && /\d/.test(password)) {
    setMessage("Strong password");
    setIsStrongPassword(true);
  }
  ```

### Task 6: Course Description Toggle (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Button toggles the visibility of the course description using useState and conditional rendering.
- **Evidence:**
  ```js
  const [isVisible, seIsVisible] = useState(false);
  ...
  <button type="submit" onClick={onSubmit}>
    Show Description
  </button>
  {isVisible && (
    <p>
      This course covers React fundamentals including components, JSX, and
      props.
    </p>
  )}
  ```

---

## Section 2: Backend & API (10 points)

### Task 7: POST /enroll API (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Implements POST /enroll, accepts JSON, and returns a confirmation message as required.
- **Evidence:**
  ```js
  app.post("/enroll", (req, res) => {
    const { userId, courseId } = req.body;
    if (!userId || !courseId) {
      res.status(400).json({ error: "Missing userId or courseId in request" });
    } else {
      res.json({ message: userId + " successfully enrolled in " + courseId });
    }
  });
  ```

### Task 8: Error Handling (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Returns 400 error and proper message when userId or courseId is missing in the request body.
- **Evidence:**
  ```js
  if (!userId || !courseId) {
    res.status(400).json({ error: "Missing userId or courseId in request" });
  }
  ```

---

## Section 3: Database (15 points)

### Task 9: Instructors Table (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Correct SQL syntax for instructors table with AUTO_INCREMENT, UNIQUE, and 3 valid inserts.
- **Evidence:**
  ```sql
  CREATE TABLE instructors ( instructor_id INT AUTO_INCREMENT PRIMARY KEY,  name VARCHAR(100), email VARCHAR(100) UNIQUE );
  INSERT INTO instructors (name, email) VALUES ('Alice Johnson', '<EMAIL>'), ('Bob Smith', '<EMAIL>'), ('Charlie Lee', '<EMAIL>');
  ```

### Task 10: User Enrollment Query (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Executes user creation, enrollment, and JOIN query to show enrolled users for a course.
- **Evidence:**
  ```sql
  INSERT INTO users (name, email, password) VALUES ('Daniel Rose','<EMAIL>', 'daniel123');
  INSERT INTO enrollments (user_id, course_id, enrollment_date) VALUES ((select user_id from users where email='<EMAIL>'), (select course_id from courses where course_name='CSS Design'), CURDATE());
  select * from  users
  join enrollments on users.user_id= enrollments.user_id
  join courses on courses.course_id=(select course_id from courses where course_name='CSS Design')
  where courses.course_id=enrollments.course_id
  ```

### Task 11: MongoDB Implementation (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Implements school, course, and enrollment models. Provides a POST route to insert a new school entry as required.
- **Evidence:**
  ```js
  // schoolModel.js
  const schoolSchema = new mongoose.Schema({
    name: String,
    address: String,
    established: Number,
  });
  // schoolRoutes.js
  router.post("/", async (req, res) => {
    const school = await School.create(req.body);
    res.send(school);
  });
  ```

---

## Section 4: AI Features (15 points)

### Task 12: Smart Search UX (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Provides a clear, practical comparison between regular and smart search, with relevant LMS examples and a detailed table.
- **Evidence:**
  > "Smart Search uses technologies like natural language processing (NLP), AI, and semantic understanding to interpret user intent..."

### Task 13: Architecture Description (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Clearly explains the roles of frontend, backend, and database, and their interactions in a full-stack LMS.
- **Evidence:**
  > "Smart Search in a Learning Management System (LMS) relies on seamless interaction between the frontend, backend, and database..."

### Task 14: Implementation Challenges (5 points)

- **Score:** 3/5
- **Level:** Developing
- **Feedback:** Identifies several challenges and conceptual solutions, but some solutions are generic and could be more specific to LMS context.
- **Evidence:**
  > "Implementing Smart Search in an LMS is a powerful enhancement, but it comes with several technical and conceptual challenges..."

---

## Grading Summary

| Section     | Task                               | Points Earned | Max Points |
| ----------- | ---------------------------------- | ------------- | ---------- |
| Frontend    | Task 1: CSS Layout Feature Boxes   | 5             | 5          |
| Frontend    | Task 2: Bootstrap Cards            | 5             | 5          |
| Frontend    | Task 3: Email Validation           | 5             | 5          |
| Frontend    | Task 4: Input Event Handling       | 4             | 5          |
| Frontend    | Task 5: Password Strength Checker  | 3             | 5          |
| Frontend    | Task 6: Course Description Toggle  | 5             | 5          |
| Backend     | Task 7: POST /enroll API           | 5             | 5          |
| Backend     | Task 8: Error Handling             | 5             | 5          |
| Database    | Task 9: Instructors Table          | 5             | 5          |
| Database    | Task 10: User Enrollment Query     | 5             | 5          |
| Database    | Task 11: MongoDB Implementation    | 5             | 5          |
| AI Features | Task 12: Smart Search UX           | 5             | 5          |
| AI Features | Task 13: Architecture Description  | 5             | 5          |
| AI Features | Task 14: Implementation Challenges | 3             | 5          |
| **TOTAL**   |                                    | **65**        | **70**     |

---

## Overall Assessment

### Strengths:

- Excellent use of modern HTML, CSS, and Bootstrap for layout and design.
- All required backend and database features are implemented and functional.
- Clear, well-structured explanations for AI features and architecture.
- Good use of React components for interactivity.

### Areas for Improvement:

- Password strength checker logic should consider passwords with exactly 6 characters as strong if they contain a number.
- Input event handling in JavaScript should attach the event listener directly to the input element for better encapsulation.
- AI implementation challenges section could be more specific to LMS context and provide deeper, actionable solutions.

### Recommendations:

- Refine password strength logic to match requirements exactly.
- Attach event listeners directly to relevant elements to avoid global side effects.
- When discussing challenges, provide more LMS-specific examples and actionable strategies.

---

## Files Evaluated:

- test/Capstone_Section1_HTML_Jena.html (HTML/CSS/Bootstrap)
- test/Capstone_Section1_JS_Jena.html (JavaScript)
- test/client/src/components/PasswordStrength.js (React Password Strength)
- test/client/src/components/CourseToggle.js (React Course Toggle)
- test/lms-backend/server.js (Express.js Backend)
- test/Capstone_Section3_SQL_Jena.sql (MySQL)
- test/mongo/models/schoolModel.js, courseModel.js, enrollmentModel.js (MongoDB)
- test/mongo/routes/schoolRoutes.js (MongoDB Route)
- test/mongo/server.js (MongoDB Server Integration)
- test/Capstone_Section4_Jena.md (AI Features)
